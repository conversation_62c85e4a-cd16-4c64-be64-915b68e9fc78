'use client'
import React, { useEffect, useState } from 'react'
import <PERSON><PERSON>ixtyViewer from './360Viewer'
import _360LandPageVideo from './_360LandPageVideo'
import { settings } from '@/lib/settings'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import NavbarWrapper from '../NavbarWrapper'
import { usePathname } from 'next/navigation'
import _360Navbar from './_360Navbar'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'

function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function _360LandingPageWrapper({query}) {
    const pathName=usePathname()
    const { experienceState,disptachExperience } = useContextExperience()
    
    useEffect(() => {
        setTimeout(() => {
            // handleLandingPageBtnClick()
        }, 1000);
    }, [])

    const handleLandingPageBtnClick = () => {
        // console.log('click')
        setLandingPageVideo(false)
        disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_NAVBAR})
    }

    const [landingPageVideo,setLandingPageVideo]=useState(true)

    // console.log('_360LandingPageWrapper:',landingPageVideo)
  
  return (
    <>
    {/* <NavbarWrapper condition={landingPageVideo}/> */}
    {/* {experienceState?.showNavbar && <_360Navbar/>} */}
    <_360Navbar/>
    {landingPageVideo 
        && query=='New_entrance_360_002'
        ?<div className='relative m-auto z-10 mb-16 h-svh w-full flex items-center justify-center'>
            <div className='flex text-white flex-col items-center justify-center z-20 absolute w-fit h-fit'>
                <div className="flex relative max-w-fit max-h-fit">
                    <ImageWrapperResponsive alt='elephant sands logo' src={settings.landingPage360.logo}/>
                </div>
                <div className="flex relative items-center flex-col uppercase mb-4">
                    <span className="font-bold text-2xl tracking-wider">your safari holiday</span>
                    <span className='text-4xl font-bold'> desitination</span>
                </div>
                <div className='flex flex-col items-center justify-center w-full h-fit'>
                    <div 
                        onClick={handleLandingPageBtnClick}
                        className='flex items-center justify-center w-fit mb-4 h-fit'
                    >
                        <BtnLandingpageComponent
                            data={settings.landingPage360.enterBtn}
                        />
                    </div>
                    <div className="flex flex-col items-center justify-center relative max-w-fit max-h-fit">
                        <ImageWrapperResponsive alt='elephant sands logo' src={settings.landingPage360.LandingPageGuide}/>
                    </div>
                </div>
            </div>
            <_360LandPageVideo videoUrl={'https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/elephantisland%2Fhero-videos%2F1755807397684.mp4?alt=media&token=4d2398a7-0f48-42c0-93a2-853eaf8be471'}/>
        </div>
        :<ThreeSixtyViewer id={query}/>}
    </>
  )
}
