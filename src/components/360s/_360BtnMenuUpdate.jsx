'use client'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useRef, useState } from 'react'
import { HiMenu, HiOutlineMenuAlt3, HiX } from 'react-icons/hi';

export default function _360BtnMenu() {
  const {experienceState,disptachExperience,menuToggle, setMenuToggle} = useContextExperience()
  const menuRef = useRef(null)

  const handleClick = () => {
    // console.log('_360BtnMenu handleClick:')
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
    setMenuToggle(!menuToggle)
  }

  // console.log('_360BtnMenuUpdate:',experienceState?.showMenu)

  return (
    <div className='menu-btn z-50 text-white flex items-center'>
      <button
        className="z-50 text-4xl mr-2"
        onClick={handleClick}
        aria-label="Toggle menu"
      >
        {/* {experienceState?.showMenu && <HiX className={`${false && 'invisible'}`} />} */}
        {experienceState?.showMenu ? <HiX className={`${!experienceState?.showMenu ? '' : 'invisible'}`} /> : <HiOutlineMenuAlt3 />}
        {/* {experienceState?.showMenu ? <HiX /> : <HiOutlineMenuAlt3 />} */}
      {/* {menuToggle && <HiX/>} */}
      </button>
    </div>
  )
}
